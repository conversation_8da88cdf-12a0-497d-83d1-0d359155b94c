@echo off
rem 请将此bat文件置于服务器客户端根目录，并修改server_port的端口参数后运行bat文件
rem 此bat脚本将在端口释放后10秒内重新运行服务器程序

chcp 65001>nul

set server_port=26900
set delay=10
echo 七日杀服务器[%server_port%]端口监控已开启，监控间隔为%delay%秒。

:loop
set usage=0
for /f "tokens=5" %%a in ('netstat -ano ^| findstr /i :%server_port%') do (set usage=1)
if %usage%==0 (start 7DaysToDieServer -quit -batchmode -nographics -configfile=serverconfig.xml –dedicated & echo 七日杀服务器[%server_port%]端口未运行，已重新运行服务端。 & timeout /t 180 /nobreak >nul)
timeout /t %delay% /nobreak >nul
goto :loop
pause