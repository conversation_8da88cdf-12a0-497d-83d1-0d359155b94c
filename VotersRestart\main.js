/****************************************************
 *        欢迎使用 Voters Version 1.0.0
 *        作者：TC130
 ****************************************************/


/****************************************************
 * 全局变量声名 及 初始化代码区
 ****************************************************/

 //var globalExample = 1;
var voters_begin = NaiwaziFunc_GetGlobalSetting('voters_begin');
var voters_agree = NaiwaziFunc_GetGlobalSetting('voters_agree');
var voters_proportion = parseInt(NaiwaziFunc_GetGlobalSetting('proportion'));
var tick = parseInt(NaiwaziFunc_GetGlobalSetting('tick'));
var restart_switch = false
var restart_tick = 30

var players_yes_list = [];
var voters_button = false;
var voters_results = false;

var dealy = 5
NaiwaziFunc_ChatMessagePrefixRegister(voters_begin);
NaiwaziFunc_ChatMessagePrefixRegister(voters_agree);
NaiwaziFunc_SetMainDelay(dealy*1000);

function Voters_Reset() {
    players_yes_list = [];
    voters_button = false;
    voters_results = false;
    tick = parseInt(NaiwaziFunc_GetGlobalSetting('tick'));
}

function isInList(list, element){
    for(var i in list){
        if(list[i] == element){
            return true;
        }
    }
    return false;
}


/****************************************************
 * 脚本主周期函数，主要逻辑在此编写，间隔1S
 * 参数: 无
 ****************************************************/

function NaiwaziEvent_ScriptMain() {
    if (voters_button){
        var all_players_count = NaiwaziFunc_GetPlayers().length;
        var yes_players_count = players_yes_list.length;
        var proportion = (yes_players_count*100/all_players_count).toFixed(2)
        if (proportion >= voters_proportion){
            NaiwaziFunc_SayPublic('', '[FFFF00]本轮投票同意占比已达到[00FF00]'+proportion+'%[FFFF00]，本次[00FF00]投票通过');
            Voters_Reset();
            restart_switch = true
        } else {
            tick = tick - dealy;
        }
        if (tick <= 0){
            NaiwaziFunc_SayPublic('', '[FFFF00]投票截至，同意占比为[FF0000]'+proportion+'%[FFFF00]，未达到'+voters_proportion+'%，本次[FF0000]投票未通过');
            Voters_Reset();
        }
    }
    if (restart_switch){
        if (restart_tick <= 0){
            NaiwaziFunc_ExecuteServerCommand('shutdown');
        } else {
            NaiwaziFunc_SayPublic('', '[FFFF00]服务器将在[FF0000]'+restart_tick+'秒[FFFF00]后关闭重启，请收起载具提前下线。');
            restart_tick = restart_tick - dealy;
        }
    }
}

//以下开始游戏事件监听函数，如有需要取消注释使用即可

/****************************************************
 * 游戏主线程调用，间隔1S
 * 参数: 无
 ****************************************************/

// function NaiwaziEvent_OnGameUpdate() {

// }


/****************************************************
 * 玩家消息调用，当注册的玩家消息来到时触发，需要使用 NaiwaziFunc_ChatMessagePrefixRegister 函数注册消息前缀
 * 参数:
 * @param {Number} chatType chatType: 聊天信息类型，取值：世界频道(0)，好友频道(1)，组队频道(2)，私聊(3)
 * @param {string} senderId senderId: 发送者的SteamID
 * @param {string} message message: 信息内容
 * @return {void}
 ****************************************************/

function NaiwaziEvent_OnChatMessage(chatType, senderId, message) {
    var player_info = NaiwaziFunc_GetEntityBySteamID(senderId);
    if (message === voters_begin){
        if (voters_button == false){
            NaiwaziFunc_SayPublic('', '[FFFF00]玩家[00FF00]【'+player_info.name+'】[FFFF00]发起了[FF0000]服务器重启[FFFF00]投票，输入[00FF00]'+voters_agree+'[FFFF00]进行确认，不输入默认为不同意');
            voters_button = true;
        } else {
            NaiwaziFunc_SayPublic('', '[FFFF00]投票正在进行中，请等待本轮投票结束后再发起。');
        }
    }
    if (message === voters_agree){
        if (voters_button){
            if (! isInList(players_yes_list,senderId)){
                players_yes_list.push(senderId)
                NaiwaziFunc_SayPublic('', '[FFFF00]玩家[00FF00]【'+player_info.name+'】[FFFF00]同意了投票');
            }
        }   
    }
}

/****************************************************
 * 当实体（非玩家）被杀死时触发
 * 参数:
 * @param {Number} killed   killed: 被杀死的实体EntityID
 * @param {Number} killer   killer: 杀人者entityID
 * @return {void}
 ****************************************************/

// function NaiwaziEvent_OnEntityKilled(killed, killer) {

// }


/****************************************************
 * 游戏关闭时触发
 * 参数: 无
 ****************************************************/

// function NaiwaziEvent_OnGameShutdown() {

// }


/****************************************************
 * 玩家离线时触发
 * 参数: 
 * @param {string} steamId   steamId:离线玩家的SteamID
 * @param {Boolean} bShutdown   bShutdown:是否时关闭游戏期间
 * @return {void}
 ****************************************************/

// function NaiwaziEvent_OnPlayerDisconnected(steamId, bShutdown) {

// }


/****************************************************
 * 玩家登录时触发
 * 参数: 
 * @param {string} steamId   steamId:登录玩家的SteamID
 * @return {void}
 ****************************************************/

// function NaiwaziEvent_OnPlayerLogin(steamId) {

// }


/****************************************************
 * 玩家在地图上重生时触发
 * 参数：
 * @param {string} steamId   steamId:玩家的SteamID
 * @param {Number} spawnType   spawnType:重生类型 死亡(2) 传送(3) 加入游戏(4 或 5)
 * @param {Number} x   x:重生地点坐标X
 * @param {Number} y   y:重生地点坐标Y
 * @param {Number} z   z:重生地点坐标Z
 * @return {void}
 ****************************************************/

// function NaiwaziEvent_OnPlayerSpawnedInWorld(steamId, spawnType, x, y, z) {

// }


/****************************************************
 * 游戏事件触发，多用于玩家死亡、加入游戏等等
 * 参数：
 * @param {string} steamId        steamId:玩家的SteamID
 * @param {Number} messageType    messageType:消息类型，纯文本(0)，玩家死亡(1)，加入游戏(2)，离开游戏(3)，改变组队(4)
 * @param {string} msg            msg:消息内容
 * @param {Number} mainName       mainName:事件主要玩家名
 * @param {Number} secondaryName  secondaryName:事件次要玩家名
 * @return {void}
 ****************************************************/

// function NaiwaziEvent_OnGameMessage(steamId, messageType, msg, mainName, secondaryName) {

// }

