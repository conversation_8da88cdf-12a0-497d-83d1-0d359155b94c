# Voters 投票重启系统 - Ubuntu版本

## 概述
这是一个适用于Ubuntu系统的七日杀服务器投票重启系统。

## 文件说明
- `main.js` - 主要的投票逻辑脚本
- `setting.xml` - 配置文件
- `monitor.sh` - Ubuntu系统的服务器监控脚本（替代Windows的monitor.bat）
- `monitor.bat` - 原Windows版本的监控脚本（保留作为参考）

## Ubuntu系统安装步骤

### 1. 准备工作
确保您的Ubuntu系统已安装lsof工具（通常系统默认已安装）：
```bash
# 检查lsof是否已安装
which lsof

# 如果没有安装，运行以下命令安装：
sudo apt update
sudo apt install lsof
```

### 2. 部署脚本
1. 将 `monitor.sh` 复制到七日杀服务器根目录
2. 设置执行权限：
```bash
chmod +x monitor.sh
```

### 3. 配置参数
编辑 `monitor.sh` 文件，根据需要修改以下参数：
- `server_port=26900` - 服务器端口号
- `delay=10` - 监控间隔时间（秒）

### 4. 启动监控
在服务器根目录运行：
```bash
./monitor.sh
```

或者在后台运行：
```bash
nohup ./monitor.sh > monitor.log 2>&1 &
```

### 5. 配置投票参数
在 `setting.xml` 中可以配置：
- `voters_begin` - 发起投票的指令（默认："服务器重启"）
- `voters_agree` - 同意投票的指令（默认："同意"）
- `tick` - 投票持续时间（默认：60秒）
- `proportion` - 投票通过所需的同意比例（默认：80%）

## 使用方法
1. 玩家在游戏中输入发起投票指令（默认："服务器重启"）
2. 其他玩家输入同意指令（默认："同意"）进行投票
3. 当同意比例达到设定值时，服务器将自动重启
4. 监控脚本会在服务器关闭后自动重新启动服务器

## 注意事项
- 确保七日杀服务器可执行文件名为 `7DaysToDieServer.x86_64`
- 如果可执行文件名不同，请修改 `monitor.sh` 中的相应行
- 监控脚本会将服务器输出重定向到 `server.log` 文件
- 建议在screen或tmux会话中运行监控脚本以便管理

## 故障排除
如果遇到问题，请检查：
1. 文件权限是否正确设置
2. 端口号是否与服务器配置一致
3. 服务器可执行文件路径是否正确
4. 查看 `server.log` 和 `monitor.log` 文件获取详细信息
