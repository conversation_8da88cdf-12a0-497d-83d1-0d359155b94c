<?xml version="1.0" encoding="UTF-8"?>
<NaiwaziManifest>
  <Version>1.0.0</Version>
  <Author>TC130（QQ1054140072）</Author>
  <Desc>服务器投票重启，使用方法：将“monitor.sh”复制到游戏根目录，设置执行权限(chmod +x monitor.sh)并运行后，启动本脚本。</Desc>
  <Settings>
      <!-- <Setting name="key1" value="value1" desc="This is description for key1" /> -->
      <!-- <Setting name="key2" value="value2" desc="This is description for key2" /> -->
      <Setting name="voters_begin" value="服务器重启" desc="发起投票指令" />
      <Setting name="voters_agree" value="同意" desc="同意投票指令" />
      <Setting name="tick" value="60" desc="投票持续时间" />
      <Setting name="proportion" value="80" desc="投票通过占比" />
  </Settings>
</NaiwaziManifest>
