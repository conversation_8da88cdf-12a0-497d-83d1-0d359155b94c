#!/bin/bash

# 七日杀服务器监控服务安装脚本

echo "正在安装七日杀服务器监控服务..."

# 获取当前用户和工作目录
CURRENT_USER=$(whoami)
CURRENT_DIR=$(pwd)

echo "当前用户: $CURRENT_USER"
echo "当前目录: $CURRENT_DIR"

# 创建服务文件内容
cat > 7dtd-monitor.service << EOF
[Unit]
Description=7 Days to Die Server Monitor
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
ExecStart=$CURRENT_DIR/monitor.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 复制服务文件到系统目录
sudo cp 7dtd-monitor.service /etc/systemd/system/

# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable 7dtd-monitor.service

echo "服务安装完成！"
echo ""
echo "使用以下命令管理服务："
echo "启动服务: sudo systemctl start 7dtd-monitor"
echo "停止服务: sudo systemctl stop 7dtd-monitor"
echo "重启服务: sudo systemctl restart 7dtd-monitor"
echo "查看状态: sudo systemctl status 7dtd-monitor"
echo "查看日志: sudo journalctl -u 7dtd-monitor -f"
echo "禁用开机自启: sudo systemctl disable 7dtd-monitor"
