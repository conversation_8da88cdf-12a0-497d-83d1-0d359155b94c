#!/bin/bash
# 请将此shell脚本置于服务器客户端根目录，并修改server_port的端口参数后运行脚本
# 此脚本将在端口释放后10秒内重新运行服务器程序

# 设置服务器端口和延迟时间
server_port=26900
delay=10

echo "七日杀服务器[${server_port}]端口监控已开启，监控间隔为${delay}秒。"

while true; do
    # 检查端口是否被占用
    usage=$(netstat -tuln | grep ":${server_port} " | wc -l)
    
    if [ $usage -eq 0 ]; then
        echo "七日杀服务器[${server_port}]端口未运行，正在重新启动服务端..."
        
        # 启动服务器（后台运行）
        nohup ./7DaysToDieServer.x86_64 -quit -batchmode -nographics -configfile=serverconfig.xml -dedicated > server.log 2>&1 &
        
        echo "七日杀服务器[${server_port}]端口未运行，已重新运行服务端。"
        
        # 等待180秒让服务器完全启动
        sleep 180
    fi
    
    # 等待指定的延迟时间
    sleep $delay
done
